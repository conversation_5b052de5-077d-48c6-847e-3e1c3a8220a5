# Generated by Django 5.2.2 on 2025-07-14 17:38

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("chatbot", "0018_alter_supportticket_brand_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="ticketproblemcategory",
            name="category",
        ),
        migrations.AlterUniqueTogether(
            name="ticketproblemcategory",
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name="ticketproblemcategory",
            name="ticket",
        ),
        migrations.AlterModelOptions(
            name="chathistory",
            options={"ordering": ["created_at"]},
        ),
        migrations.RemoveField(
            model_name="chathistory",
            name="context_used",
        ),
        migrations.RemoveField(
            model_name="chathistory",
            name="source_files",
        ),
        migrations.RemoveField(
            model_name="chathistory",
            name="timestamp",
        ),
        migrations.RemoveField(
            model_name="supportticket",
            name="final_prompt",
        ),
        migrations.RemoveField(
            model_name="supportticket",
            name="model",
        ),
        migrations.RemoveField(
            model_name="supportticket",
            name="operating_system",
        ),
        migrations.RemoveField(
            model_name="supportticket",
            name="product_name",
        ),
        migrations.RemoveField(
            model_name="supportticket",
            name="serial_no",
        ),
        migrations.AddField(
            model_name="chathistory",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True,
                default=django.utils.timezone.now,
                help_text="When this chat exchange occurred",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="supportticket",
            name="generated_prompt",
            field=models.TextField(
                blank=True, help_text="Generated prompt for GPT", null=True
            ),
        ),
        migrations.AddField(
            model_name="supportticket",
            name="problem_categories",
            field=models.JSONField(
                blank=True, default=list, help_text="Selected problem categories"
            ),
        ),
        migrations.AddField(
            model_name="supportticket",
            name="prompt_file_path",
            field=models.CharField(
                blank=True,
                help_text="Path to external JSON prompt file",
                max_length=500,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="chathistory",
            name="bot_response",
            field=models.TextField(help_text="The bot's response to the user message"),
        ),
        migrations.AlterField(
            model_name="chathistory",
            name="ticket",
            field=models.ForeignKey(
                help_text="The support ticket this message belongs to",
                on_delete=django.db.models.deletion.CASCADE,
                related_name="chat_messages",
                to="chatbot.supportticket",
            ),
        ),
        migrations.AlterField(
            model_name="chathistory",
            name="user_message",
            field=models.TextField(help_text="The user's message/query"),
        ),
        migrations.AlterField(
            model_name="supportticket",
            name="brand",
            field=models.CharField(
                blank=True,
                choices=[
                    ("DALSA", "DALSA"),
                    ("FLIR", "FLIR"),
                    ("Basler", "Basler"),
                    ("Allied Vision", "Allied Vision"),
                    ("Cognex", "Cognex"),
                    ("Other", "Other"),
                ],
                max_length=50,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="supportticket",
            name="camera_configuration_tool",
            field=models.CharField(
                blank=True,
                choices=[
                    ("IP Config Tool", "IP Config Tool"),
                    ("eBUS Player", "eBUS Player"),
                    ("Pylon Viewer", "Pylon Viewer"),
                    ("CamExpert", "CamExpert"),
                    ("Other", "Other"),
                ],
                max_length=100,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="supportticket",
            name="family_name",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Genie Nano", "Genie Nano"),
                    ("Linea", "Linea"),
                    ("Ace", "Ace"),
                    ("Dart", "Dart"),
                    ("Other", "Other"),
                ],
                max_length=100,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="supportticket",
            name="model_number",
            field=models.CharField(
                blank=True, help_text="e.g., C1280M-25G", max_length=100, null=True
            ),
        ),
        migrations.AlterField(
            model_name="supportticket",
            name="operating_system_detailed",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Windows 10", "Windows 10"),
                    ("Windows 11", "Windows 11"),
                    ("Linux Ubuntu 20.04", "Linux Ubuntu 20.04"),
                    ("Linux Ubuntu 22.04", "Linux Ubuntu 22.04"),
                    ("CentOS", "CentOS"),
                    ("Other", "Other"),
                ],
                max_length=100,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="supportticket",
            name="programming_language",
            field=models.CharField(
                blank=True,
                choices=[
                    ("C#", "C#"),
                    ("C++", "C++"),
                    ("Python", "Python"),
                    ("Java", "Java"),
                    ("LabVIEW", "LabVIEW"),
                    ("Other", "Other"),
                ],
                max_length=50,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="supportticket",
            name="sdk_software_used",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Sapera LT", "Sapera LT"),
                    ("Spinnaker", "Spinnaker"),
                    ("Pylon", "Pylon"),
                    ("VisionPoint", "VisionPoint"),
                    ("Other", "Other"),
                ],
                max_length=100,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="supportticket",
            name="sdk_version",
            field=models.CharField(
                blank=True, help_text="e.g., v6.10", max_length=50, null=True
            ),
        ),
        migrations.AlterField(
            model_name="supportticket",
            name="sensor_type",
            field=models.CharField(
                blank=True,
                choices=[("Area Scan", "Area Scan"), ("Line Scan", "Line Scan")],
                max_length=50,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="supportticket",
            name="serial_number",
            field=models.CharField(
                blank=True, help_text="e.g., 12345678", max_length=100, null=True
            ),
        ),
        migrations.AddIndex(
            model_name="chathistory",
            index=models.Index(
                fields=["ticket", "created_at"], name="chat_histor_ticket__8543cf_idx"
            ),
        ),
        migrations.AlterModelTable(
            name="chathistory",
            table="chat_history",
        ),
        migrations.DeleteModel(
            name="ProblemCategory",
        ),
        migrations.DeleteModel(
            name="TicketProblemCategory",
        ),
    ]
